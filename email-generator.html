<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>随机邮箱生成器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .email-display {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            font-size: 1.2em;
            font-family: 'Courier New', monospace;
            color: #495057;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            word-break: break-all;
        }
        
        .generate-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            margin: 10px;
        }
        
        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }
        
        .copy-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
            margin: 10px;
            opacity: 0.7;
            pointer-events: none;
        }
        
        .copy-btn.active {
            opacity: 1;
            pointer-events: auto;
        }
        
        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.6);
        }
        
        .description {
            color: #6c757d;
            margin-bottom: 30px;
            font-size: 1.1em;
            line-height: 1.6;
        }
        
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }
        
        .toast.show {
            transform: translateX(0);
        }
        
        .stats {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            color: #6c757d;
        }
        
        .stats h3 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        
        .count {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎲 随机邮箱生成器</h1>
        <p class="description">
            点击下方按钮，即可生成一个随机的@shusj.xyz邮箱地址<br>
            每次生成的邮箱都是独一无二的！
        </p>
        
        <div class="email-display" id="emailDisplay">
            点击"生成邮箱"开始使用
        </div>
        
        <button class="generate-btn" onclick="generateEmail()">
            🎯 生成邮箱
        </button>
        
        <button class="copy-btn" id="copyBtn" onclick="copyEmail()">
            📋 复制邮箱
        </button>
        
        <div class="stats">
            <h3>生成统计</h3>
            <div class="count" id="generateCount">0</div>
            <div>已生成邮箱数量</div>
        </div>
    </div>
    
    <div class="toast" id="toast">
        邮箱已复制到剪贴板！
    </div>

    <script>
        let currentEmail = '';
        let generateCount = 0;
        
        // 预定义的用户名前缀
        const prefixes = [
            'user', 'admin', 'test', 'demo', 'temp', 'guest', 'member', 'client',
            'student', 'teacher', 'manager', 'developer', 'designer', 'writer',
            'editor', 'reviewer', 'analyst', 'consultant', 'expert', 'specialist',
            'assistant', 'helper', 'support', 'service', 'contact', 'info',
            'hello', 'welcome', 'new', 'active', 'premium', 'vip', 'pro',
            'beta', 'alpha', 'trial', 'free', 'basic', 'standard', 'advanced'
        ];
        
        // 随机字符串生成函数
        function generateRandomString(length) {
            const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }
        
        // 生成随机邮箱
        function generateEmail() {
            const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
            const randomPart = generateRandomString(Math.floor(Math.random() * 8) + 3); // 3-10位随机字符
            const separator = Math.random() > 0.5 ? '' : (Math.random() > 0.5 ? '.' : '_');
            
            currentEmail = prefix + separator + randomPart + '@shusj.xyz';
            
            document.getElementById('emailDisplay').textContent = currentEmail;
            
            // 激活复制按钮
            const copyBtn = document.getElementById('copyBtn');
            copyBtn.classList.add('active');
            
            // 更新生成计数
            generateCount++;
            document.getElementById('generateCount').textContent = generateCount;
            
            // 保存到本地存储
            localStorage.setItem('emailGenerateCount', generateCount);
        }
        
        // 复制邮箱到剪贴板
        function copyEmail() {
            if (!currentEmail) return;
            
            navigator.clipboard.writeText(currentEmail).then(function() {
                showToast();
            }).catch(function(err) {
                // 降级方案：使用传统方法复制
                const textArea = document.createElement('textarea');
                textArea.value = currentEmail;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast();
            });
        }
        
        // 显示复制成功提示
        function showToast() {
            const toast = document.getElementById('toast');
            toast.classList.add('show');
            setTimeout(() => {
                toast.classList.remove('show');
            }, 2000);
        }
        
        // 页面加载时恢复生成计数
        window.onload = function() {
            const savedCount = localStorage.getItem('emailGenerateCount');
            if (savedCount) {
                generateCount = parseInt(savedCount);
                document.getElementById('generateCount').textContent = generateCount;
            }
        };
        
        // 键盘快捷键支持
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' || event.key === ' ') {
                generateEmail();
                event.preventDefault();
            } else if (event.ctrlKey && event.key === 'c' && currentEmail) {
                copyEmail();
                event.preventDefault();
            }
        });
    </script>
</body>
</html>
