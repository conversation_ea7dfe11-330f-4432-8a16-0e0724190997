<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱生成器插件演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .demo-section {
            background: white;
            padding: 30px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .demo-section h2 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        .api-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .api-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }
        
        .api-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .api-item button {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px 5px 5px 0;
        }
        
        .api-item button:hover {
            background: #5a6fd8;
        }
        
        .result {
            background: white;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            color: #495057;
        }
        
        @media (max-width: 768px) {
            .api-demo {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 随机邮箱生成器插件演示</h1>
        
        <div class="demo-section">
            <h2>🎯 插件小部件演示</h2>
            <p>这是一个完整的UI组件，可以直接嵌入到任何网页中：</p>
            <div id="emailWidget"></div>
        </div>
        
        <div class="demo-section">
            <h2>🔧 API 功能演示</h2>
            <p>以下是插件提供的各种API功能：</p>
            
            <div class="api-demo">
                <div class="api-item">
                    <h4>生成单个邮箱</h4>
                    <button onclick="generateSingle()">生成邮箱</button>
                    <div class="result" id="singleResult">点击按钮生成邮箱</div>
                </div>
                
                <div class="api-item">
                    <h4>批量生成邮箱</h4>
                    <button onclick="generateBatch()">生成5个邮箱</button>
                    <div class="result" id="batchResult">点击按钮批量生成</div>
                </div>
                
                <div class="api-item">
                    <h4>获取当前邮箱</h4>
                    <button onclick="getCurrent()">获取当前邮箱</button>
                    <div class="result" id="currentResult">暂无当前邮箱</div>
                </div>
                
                <div class="api-item">
                    <h4>获取生成统计</h4>
                    <button onclick="getStats()">查看统计</button>
                    <div class="result" id="statsResult">点击查看统计信息</div>
                </div>
                
                <div class="api-item">
                    <h4>复制到剪贴板</h4>
                    <button onclick="copyEmail()">复制当前邮箱</button>
                    <div class="result" id="copyResult">先生成邮箱再复制</div>
                </div>
                
                <div class="api-item">
                    <h4>重置计数</h4>
                    <button onclick="resetStats()">重置统计</button>
                    <div class="result" id="resetResult">点击重置生成计数</div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>📖 使用说明</h2>
            
            <h3>1. 引入插件</h3>
            <div class="code-block">
&lt;script src="email-generator-plugin.js"&gt;&lt;/script&gt;
            </div>
            
            <h3>2. 基本使用</h3>
            <div class="code-block">
// 生成邮箱
const email = EmailGenerator.generate();
console.log(email); // 输出: <EMAIL>

// 复制到剪贴板
EmailGenerator.copyToClipboard();

// 获取生成统计
const count = EmailGenerator.getGenerateCount();
            </div>
            
            <h3>3. 创建UI组件</h3>
            <div class="code-block">
// 在页面中创建一个div容器
&lt;div id="myEmailGenerator"&gt;&lt;/div&gt;

// 初始化组件
EmailGenerator.createWidget('myEmailGenerator');
            </div>
            
            <h3>4. 批量生成</h3>
            <div class="code-block">
// 生成5个邮箱
const emails = EmailGenerator.generateBatch(5);
console.log(emails);
// 输出: ['<EMAIL>', '<EMAIL>', ...]
            </div>
        </div>
        
        <div class="demo-section">
            <h2>✨ 特性说明</h2>
            <ul>
                <li><strong>随机性强</strong>：使用多种前缀和随机字符组合，确保生成的邮箱具有良好的随机性</li>
                <li><strong>统计功能</strong>：自动记录生成次数，数据持久化保存在本地存储中</li>
                <li><strong>复制功能</strong>：支持一键复制到剪贴板，兼容新旧浏览器</li>
                <li><strong>批量生成</strong>：支持一次性生成多个邮箱地址</li>
                <li><strong>UI组件</strong>：提供开箱即用的UI组件，可直接嵌入网页</li>
                <li><strong>兼容性好</strong>：支持现代浏览器和模块化导入</li>
                <li><strong>轻量级</strong>：纯JavaScript实现，无外部依赖</li>
            </ul>
        </div>
    </div>
    
    <!-- 引入插件 -->
    <script src="email-generator-plugin.js"></script>
    
    <script>
        // 初始化UI组件
        EmailGenerator.createWidget('emailWidget');
        
        // API演示函数
        function generateSingle() {
            const email = EmailGenerator.generate();
            document.getElementById('singleResult').textContent = email;
        }
        
        function generateBatch() {
            const emails = EmailGenerator.generateBatch(5);
            document.getElementById('batchResult').innerHTML = emails.join('<br>');
        }
        
        function getCurrent() {
            const current = EmailGenerator.getCurrentEmail();
            document.getElementById('currentResult').textContent = current || '暂无当前邮箱';
        }
        
        function getStats() {
            const count = EmailGenerator.getGenerateCount();
            document.getElementById('statsResult').textContent = `已生成 ${count} 个邮箱`;
        }
        
        function copyEmail() {
            const current = EmailGenerator.getCurrentEmail();
            if (current) {
                EmailGenerator.copyToClipboard().then(success => {
                    document.getElementById('copyResult').textContent = 
                        success ? '复制成功！' : '复制失败';
                });
            } else {
                document.getElementById('copyResult').textContent = '请先生成邮箱';
            }
        }
        
        function resetStats() {
            EmailGenerator.resetCount();
            document.getElementById('resetResult').textContent = '统计已重置';
            // 更新其他显示
            getStats();
        }
    </script>
</body>
</html>
