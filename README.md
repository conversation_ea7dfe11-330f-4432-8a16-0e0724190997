# 随机邮箱生成器插件

一个简单易用的随机邮箱生成器，专门生成 @shusj.xyz 结尾的邮箱地址。

## 📁 文件说明

### 1. email-generator.html
完整的独立网页应用，包含：
- 美观的用户界面
- 一键生成随机邮箱
- 复制到剪贴板功能
- 生成统计功能
- 响应式设计

**使用方法**：直接在浏览器中打开此文件即可使用。

### 2. email-generator-plugin.js
JavaScript插件文件，可以嵌入到任何网页中：
- 提供完整的API接口
- 支持批量生成
- 自动统计功能
- 可创建UI组件
- 兼容现代浏览器

### 3. plugin-demo.html
插件使用演示页面，展示：
- 插件的各种功能
- API使用示例
- 集成方法说明
- 完整的使用文档

## 🚀 快速开始

### 方法一：直接使用完整应用
```bash
# 在浏览器中打开
file:///path/to/email-generator.html
```

### 方法二：集成到现有网页
```html
<!-- 引入插件 -->
<script src="email-generator-plugin.js"></script>

<!-- 创建容器 -->
<div id="emailGenerator"></div>

<script>
// 创建UI组件
EmailGenerator.createWidget('emailGenerator');
</script>
```

### 方法三：使用API
```javascript
// 生成单个邮箱
const email = EmailGenerator.generate();
console.log(email); // 例如: <EMAIL>

// 批量生成
const emails = EmailGenerator.generateBatch(5);

// 复制到剪贴板
EmailGenerator.copyToClipboard();

// 获取统计信息
const count = EmailGenerator.getGenerateCount();
```

## 🎯 主要功能

- ✅ **随机生成**：使用多种前缀和随机字符组合
- ✅ **一键复制**：支持复制到剪贴板
- ✅ **统计功能**：记录生成次数，持久化存储
- ✅ **批量生成**：支持一次生成多个邮箱
- ✅ **UI组件**：提供开箱即用的界面组件
- ✅ **响应式**：适配各种屏幕尺寸
- ✅ **兼容性**：支持现代浏览器
- ✅ **轻量级**：纯JavaScript，无外部依赖

## 📖 API 文档

### EmailGenerator.generate()
生成一个随机邮箱地址
- **返回值**：字符串，生成的邮箱地址

### EmailGenerator.generateBatch(count)
批量生成邮箱地址
- **参数**：count (数字) - 要生成的邮箱数量，默认5个
- **返回值**：数组，包含生成的邮箱地址

### EmailGenerator.getCurrentEmail()
获取当前生成的邮箱地址
- **返回值**：字符串，当前邮箱地址

### EmailGenerator.getGenerateCount()
获取总生成次数
- **返回值**：数字，生成次数

### EmailGenerator.copyToClipboard(email)
复制邮箱到剪贴板
- **参数**：email (字符串，可选) - 要复制的邮箱，默认为当前邮箱
- **返回值**：Promise，复制是否成功

### EmailGenerator.createWidget(containerId)
在指定容器中创建UI组件
- **参数**：containerId (字符串) - 容器元素的ID

### EmailGenerator.resetCount()
重置生成计数
- **返回值**：无

## 🌟 使用示例

查看 `plugin-demo.html` 文件获取完整的使用示例和演示。

## 📝 注意事项

1. 生成的邮箱地址仅用于演示目的
2. 统计数据保存在浏览器本地存储中
3. 复制功能需要HTTPS环境或本地文件访问
4. 建议在现代浏览器中使用以获得最佳体验

## 🔧 自定义配置

可以通过修改 `email-generator-plugin.js` 中的 `prefixes` 数组来自定义邮箱前缀：

```javascript
prefixes: [
    'custom1', 'custom2', 'custom3'
    // 添加您自己的前缀
]
```

## 📞 技术支持

如有问题或建议，请联系开发者。
